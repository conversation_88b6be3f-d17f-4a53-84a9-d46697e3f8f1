use crate::app::App;
use crate::llm_client;
use reedline::{Prompt, PromptEditMode, PromptHistorySearch, PromptHistorySearchStatus};
use std::borrow::Cow;
use std::sync::{Arc, Mutex};
use tiktoken_rs::{cl100k_base, CoreBPE};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct CurrentPromptBufferState {
    pub current_buffer_content: String,
}

pub struct TokenCountingPrompt {
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    tokenizer: Arc<CoreBPE>,
    base_prompt_text: String,
}

impl TokenCountingPrompt {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
        base_prompt_text: String,
    ) -> Self {
        let tokenizer = Arc::new(cl100k_base().unwrap_or_else(|e| {
            eprintln!(
                "Failed to load cl100k_base tokenizer: {}. Token counting will be inaccurate.",
                e
            );
            panic!("Tokenizer cl100k_base failed to load: {}", e);
        }));
        Self {
            app_arc,
            prompt_buffer_state,
            tokenizer,
            base_prompt_text,
        }
    }
}

impl Prompt for TokenCountingPrompt {
    fn render_prompt_left(&self) -> Cow<str> {
        Cow::Owned(self.base_prompt_text.clone())
    }

    fn render_prompt_right(&self) -> Cow<str> {
        let app_guard = self.app_arc.lock().unwrap();
        let prompt_state_guard = self.prompt_buffer_state.lock().unwrap();

        let mut total_tokens = 0;

        // System prompt tokens
        total_tokens += self
            .tokenizer
            .encode_with_special_tokens(llm_client::SYSTEM_PROMPT)
            .len();

        // History tokens
        for message in app_guard.conversation_history_for_llm.iter() {
            total_tokens += self
                .tokenizer
                .encode_with_special_tokens(&message.content)
                .len();
        }

        // Current input buffer tokens
        total_tokens += self
            .tokenizer
            .encode_with_special_tokens(&prompt_state_guard.current_buffer_content)
            .len();

        Cow::Owned(format!("[{} tk]", total_tokens))
    }

    fn render_prompt_indicator(&self, _edit_mode: PromptEditMode) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_multiline_indicator(&self) -> Cow<str> {
        Cow::Borrowed("::: ")
    }

    fn render_prompt_history_search_indicator(
        &self,
        history_search: PromptHistorySearch,
    ) -> Cow<str> {
        let prefix = match history_search.status {
            PromptHistorySearchStatus::Passing => "",
            PromptHistorySearchStatus::Failing => "failing ",
        };
        Cow::Owned(format!(
            "({}reverse-search: {}) ",
            prefix, history_search.term
        ))
    }
}
